"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/components/i18n-provider";
import { AdminRouteGuard } from "@/components/admin-route-guard";
import { Sidebar } from "@/components/sidebar";
import { HeaderControls } from "@/components/header-controls";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Menu,
  Radio,
  Users,
  MessageSquare,
  TrendingUp,
  Send,
  Clock,
  Activity,
  BarChart3,
} from "lucide-react";
import Link from "next/link";

export default function BroadcastPage() {
  const { t } = useTranslation("common");
  const { isRTL } = useLanguage();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Form state
  const [messageTitle, setMessageTitle] = useState("");
  const [messageContent, setMessageContent] = useState("");
  const [targetAudience, setTargetAudience] = useState("allUsers");
  const [priority, setPriority] = useState("normal");
  const [scheduleType, setScheduleType] = useState("sendNow");
  const [scheduledDate, setScheduledDate] = useState("");
  const [scheduledTime, setScheduledTime] = useState("");

  const handleSendMessage = async () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      // Reset form
      setMessageTitle("");
      setMessageContent("");
      setTargetAudience("allUsers");
      setPriority("normal");
      setScheduleType("sendNow");
      setScheduledDate("");
      setScheduledTime("");
    }, 2000);
  };

  // Mock data for statistics
  const stats = {
    totalMessages: 1234,
    activeUsers: 567,
    messagesSentToday: 89,
    deliveryRate: 96.2,
  };

  const quickStats = {
    messagesThisWeek: 45,
    averageOpenRate: 78.5,
    totalRecipients: 2847,
    lastMessageSent: "2 hours ago",
  };

  const recentActivity = [
    {
      id: 1,
      type: "broadcast",
      title: "System Maintenance Notice",
      status: "delivered",
      recipients: 1234,
      time: "2 hours ago",
    },
    {
      id: 2,
      type: "notification",
      title: "New Feature Announcement",
      status: "sent",
      recipients: 987,
      time: "5 hours ago",
    },
    {
      id: 3,
      type: "alert",
      title: "Security Update Required",
      status: "delivered",
      recipients: 2156,
      time: "1 day ago",
    },
  ];

  return (
    <AdminRouteGuard>
      <div
        className={`min-h-screen bg-background ${isRTL ? "font-tajawal" : "font-sans"}`}
      >
        {/* Header */}
        <header
          className={`bg-background border-b border-border px-4 sm:px-5 py-4 ${isRTL ? "lg:mr-72" : "lg:ml-72"}`}
        >
          <div className="flex items-center justify-between">
            <div
              className={`flex items-center ${isRTL ? "space-x-reverse space-x-2" : "space-x-2"}`}
            >
              {/* Mobile Hamburger Menu */}
              <button
                onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                className="cursor-pointer lg:hidden p-2 hover:bg-muted rounded-lg transition-colors min-h-[36px] min-w-[36px] flex items-center justify-center"
                aria-label="Toggle navigation menu"
              >
                <Menu className="w-5 h-5 text-muted-foreground" />
              </button>
              <Link
                href="/"
                className="flex items-center gap-1 sm:gap-2 cursor-pointer"
              >
                <div
                  className={`${isRTL && "ml-2"} w-8 h-8 bg-primary rounded-lg flex items-center justify-center`}
                >
                  <div className="w-4 h-4 bg-primary-foreground rounded-sm"></div>
                </div>
                <span className="text-base sm:text-xl font-semibold text-foreground">
                  {t("header.logo")}
                </span>
              </Link>
            </div>
            <HeaderControls />
          </div>
        </header>

        <div className="flex">
          {/* Sidebar */}
          <Sidebar
            isOpen={isSidebarOpen}
            onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
          />

          {/* Main Content */}
          <main
            className={`flex-1 ${isRTL ? "lg:mr-72" : "lg:ml-72"} p-4 sm:p-5 space-y-4 sm:space-y-5 bg-secondary/50`}
          >
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-5">
              {/* Total Messages Card */}
              <Card className="group dark:bg-muted/50">
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="space-y-3 sm:space-y-4 flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                          <MessageSquare className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                            {t("broadcast.stats.totalMessages.title")}
                          </h3>
                          <p className="text-xs sm:text-sm text-muted-foreground/80">
                            {t("broadcast.stats.totalMessages.description")}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-baseline space-x-2">
                          <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                            {stats.totalMessages.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Active Users Card */}
              <Card className="group dark:bg-muted/50">
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="space-y-3 sm:space-y-4 flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                          <Users className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                            {t("broadcast.stats.activeUsers.title")}
                          </h3>
                          <p className="text-xs sm:text-sm text-muted-foreground/80">
                            {t("broadcast.stats.activeUsers.description")}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-baseline space-x-2">
                          <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                            {stats.activeUsers.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Messages Sent Today Card */}
              <Card className="group dark:bg-muted/50">
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="space-y-3 sm:space-y-4 flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                          <Send className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                            {t("broadcast.stats.messagesSentToday.title")}
                          </h3>
                          <p className="text-xs sm:text-sm text-muted-foreground/80">
                            {t("broadcast.stats.messagesSentToday.description")}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-baseline space-x-2">
                          <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                            {stats.messagesSentToday.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Rate Card */}
              <Card className="group dark:bg-muted/50">
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="space-y-3 sm:space-y-4 flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primary/10 border border-primary/10 rounded-xl flex items-center justify-center">
                          <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-base lg:text-lg font-medium text-foreground uppercase tracking-wide">
                            {t("broadcast.stats.deliveryRate.title")}
                          </h3>
                          <p className="text-xs sm:text-sm text-muted-foreground/80">
                            {t("broadcast.stats.deliveryRate.description")}
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-baseline space-x-2">
                          <span className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground group-hover:text-primary transition-colors">
                            {stats.deliveryRate}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content Area */}
            <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 sm:gap-5">
              {/* Broadcast Form - Center (3 columns) */}
              <div className="lg:col-span-4">
                <Card className="dark:bg-muted/50">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                        <Radio className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-lg font-semibold text-foreground">
                          {t("broadcast.form.title")}
                        </CardTitle>
                        <p className="text-xs text-muted-foreground">
                          {t("broadcast.form.description")}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Message Title */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="messageTitle"
                        className="text-sm font-medium"
                      >
                        {t("broadcast.form.messageTitle")}
                      </Label>
                      <Input
                        id="messageTitle"
                        value={messageTitle}
                        onChange={(e) => setMessageTitle(e.target.value)}
                        placeholder={t(
                          "broadcast.form.placeholders.messageTitle"
                        )}
                        className="w-full"
                      />
                    </div>

                    {/* Message Content */}
                    <div className="space-y-2">
                      <Label
                        htmlFor="messageContent"
                        className="text-sm font-medium"
                      >
                        {t("broadcast.form.messageContent")}
                      </Label>
                      <Textarea
                        id="messageContent"
                        value={messageContent}
                        onChange={(e) => setMessageContent(e.target.value)}
                        placeholder={t(
                          "broadcast.form.placeholders.messageContent"
                        )}
                        rows={6}
                        className="w-full resize-none"
                      />
                    </div>

                    {/* Target Audience */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        {t("broadcast.form.targetAudience")}
                      </Label>
                      <Select
                        value={targetAudience}
                        onValueChange={setTargetAudience}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="allUsers">
                            {t("broadcast.form.allUsers")}
                          </SelectItem>
                          <SelectItem value="activeUsers">
                            {t("broadcast.form.activeUsers")}
                          </SelectItem>
                          <SelectItem value="premiumUsers">
                            {t("broadcast.form.premiumUsers")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Priority */}
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">
                        {t("broadcast.form.priority")}
                      </Label>
                      <Select value={priority} onValueChange={setPriority}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">
                            {t("broadcast.form.low")}
                          </SelectItem>
                          <SelectItem value="normal">
                            {t("broadcast.form.normal")}
                          </SelectItem>
                          <SelectItem value="high">
                            {t("broadcast.form.high")}
                          </SelectItem>
                          <SelectItem value="urgent">
                            {t("broadcast.form.urgent")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Schedule Message */}
                    <div className="space-y-4">
                      <Label className="text-sm font-medium">
                        {t("broadcast.form.scheduleMessage")}
                      </Label>
                      <RadioGroup
                        value={scheduleType}
                        onValueChange={setScheduleType}
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="sendNow" id="sendNow" />
                          <Label htmlFor="sendNow" className="text-sm">
                            {t("broadcast.form.sendNow")}
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem
                            value="scheduleForLater"
                            id="scheduleForLater"
                          />
                          <Label htmlFor="scheduleForLater" className="text-sm">
                            {t("broadcast.form.scheduleForLater")}
                          </Label>
                        </div>
                      </RadioGroup>

                      {scheduleType === "scheduleForLater" && (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                          <div className="space-y-2">
                            <Label
                              htmlFor="scheduledDate"
                              className="text-sm font-medium"
                            >
                              {t("broadcast.form.scheduledDate")}
                            </Label>
                            <Input
                              id="scheduledDate"
                              type="date"
                              value={scheduledDate}
                              onChange={(e) => setScheduledDate(e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label
                              htmlFor="scheduledTime"
                              className="text-sm font-medium"
                            >
                              {t("broadcast.form.scheduledTime")}
                            </Label>
                            <Input
                              id="scheduledTime"
                              type="time"
                              value={scheduledTime}
                              onChange={(e) => setScheduledTime(e.target.value)}
                            />
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Send Button */}
                    <div className="pt-4">
                      <Button
                        onClick={handleSendMessage}
                        disabled={isLoading || !messageTitle || !messageContent}
                        className="w-full sm:w-auto"
                      >
                        {isLoading ? (
                          <>
                            <Clock className="w-4 h-4 mr-2 animate-spin" />
                            {t("broadcast.form.sending")}
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            {t("broadcast.form.send")}
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Left Sidebar - Quick Stats and Recent Activity */}
              <div className="lg:col-span-2 space-y-4 sm:space-y-5">
                {/* Quick Statistics Card */}
                <Card className="dark:bg-muted/50">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                        <BarChart3 className="w-4 h-4 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-semibold text-foreground">
                          {t("broadcast.quickStats.title")}
                        </CardTitle>
                        <p className="text-xs text-muted-foreground">
                          {t("broadcast.quickStats.description")}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {t("broadcast.quickStats.messagesThisWeek")}
                        </span>
                        <span className="text-sm font-semibold text-foreground">
                          {quickStats.messagesThisWeek}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {t("broadcast.quickStats.averageOpenRate")}
                        </span>
                        <span className="text-sm font-semibold text-foreground">
                          {quickStats.averageOpenRate}%
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {t("broadcast.quickStats.totalRecipients")}
                        </span>
                        <span className="text-sm font-semibold text-foreground">
                          {quickStats.totalRecipients.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">
                          {t("broadcast.quickStats.lastMessageSent")}
                        </span>
                        <span className="text-sm font-semibold text-foreground">
                          {quickStats.lastMessageSent}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity Card */}
                <Card className="dark:bg-muted/50">
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-primary/10 border border-primary/10 rounded-lg flex items-center justify-center">
                        <Activity className="w-4 h-4 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-base font-semibold text-foreground">
                          {t("broadcast.recentActivity.title")}
                        </CardTitle>
                        <p className="text-xs text-muted-foreground">
                          {t("broadcast.recentActivity.description")}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {recentActivity.length > 0 ? (
                      <div className="space-y-3">
                        {recentActivity.map((activity) => (
                          <div
                            key={activity.id}
                            className="flex items-start space-x-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                          >
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <p className="text-sm font-medium text-foreground truncate">
                                  {activity.title}
                                </p>
                                <span
                                  className={`text-xs px-2 py-1 rounded-full ${
                                    activity.status === "delivered"
                                      ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                      : activity.status === "sent"
                                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                                        : activity.status === "failed"
                                          ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                                  }`}
                                >
                                  {t(
                                    `broadcast.recentActivity.status.${activity.status}`
                                  )}
                                </span>
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                {activity.recipients.toLocaleString()}{" "}
                                recipients • {activity.time}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Activity className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                        <p className="text-sm text-muted-foreground">
                          {t("broadcast.recentActivity.noActivity")}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {t("broadcast.recentActivity.noActivityDescription")}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </main>
        </div>
      </div>
    </AdminRouteGuard>
  );
}
